import type { Metada<PERSON> } from "next";
import { cn } from "@/lib/utils";
import { ThemeProvider } from "@/utils/theme-provider";
import "../globals.css";
import Sidebar from "./_components/sidebar";
import Navbar from "./_components/navbar";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
        {/* Sidebar */}
        <div className="max-md:hidden md:flex h-full w-64 flex-col fixed inset-y-0 z-50">
          <Sidebar />
        </div>

        {/* Mobile Navbar */}
        <div className="md:hidden h-[70px] md:pl-64 fixed inset-y-0 w-full z-50 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
          <Navbar />
        </div>

        {/* Main Content */}
        <main className="md:pl-64 max-md:mt-16 min-h-screen">
          <div className="p-4 max-md:p-2">
            <div className="max-w-full">{children}</div>
          </div>
        </main>
      </ThemeProvider>
    </div>
  );
}
