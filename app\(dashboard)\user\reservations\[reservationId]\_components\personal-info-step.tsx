"use client";

import { useCurrentUser } from "@/hooks/use-current-user";
import DashboardPageTitle from "@/app/(dashboard)/_components/dashboard-page-title";
import { Separator } from "@/components/ui/separator";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import SectionTitle from "../../../settings/_components/section-title";
import PersonalInfoForm from "../../../settings/_components/personal-info-form";
import axios from "axios";
import toast from "react-hot-toast";

const page = () => {
  const user = useCurrentUser();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  const isAuthenticated = user !== null;
  const isOnboarded = user?.isOnboarded ?? false;

  const isAllFieldsCompleted =
    user?.firstName &&
    user?.lastName &&
    user?.phone &&
    user?.city &&
    user?.country &&
    user?.birthday;

  // Auto-onboard user if all fields are completed but not yet onboarded
  useEffect(() => {
    if (
      isAllFieldsCompleted &&
      !isOnboarded &&
      !isSubmitting &&
      !showSuccessMessage
    ) {
      setShowSuccessMessage(true);
    }
  }, [isAllFieldsCompleted, isOnboarded, isSubmitting, showSuccessMessage]);

  // If not authenticated, redirect to the sign-up page
  if (!isAuthenticated) {
    router.push("/sign-in");
    return null;
  }

  // Function to handle the onboarding process
  const handleOnboarding = async () => {
    setIsSubmitting(true);

    try {
      // Update user onboarded status in the database
      await axios.patch("/api/user/onboard", {
        isOnboarded: true,
      });

      setShowSuccessMessage(true);
      toast.success("Profil mis à jour avec succès !");
      router.refresh(); // Refresh to update user state
      setIsSubmitting(false);
    } catch (error) {
      console.error("Failed to update user profile:", error);
      toast.error("Erreur lors de la mise à jour du profil");
      setIsSubmitting(false);
    }
  };

  // If the user is authenticated but not onboarded
  if (isAuthenticated && !isOnboarded && !showSuccessMessage) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-8 shadow-lg">
          <div className="mb-6">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              Complétez vos informations personnelles
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Remplissez tous les champs requis pour finaliser votre profil et
              continuer votre réservation
            </p>
          </div>

          {/* Progress Indicator */}
          <div className="mb-6">
            <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-2">
              <span>Progression du profil</span>
              <span>{isAllFieldsCompleted ? "100%" : "En cours..."}</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  isAllFieldsCompleted
                    ? "bg-green-500 w-full"
                    : "bg-yellow-500 w-3/4"
                }`}
              />
            </div>
          </div>

          {/* Alert Message */}
          <div
            className={`p-4 rounded-lg border mb-6 ${
              isAllFieldsCompleted
                ? "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-200"
                : "bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200"
            }`}
          >
            <div className="flex items-start">
              <div className="flex-shrink-0">
                {isAllFieldsCompleted ? (
                  <svg
                    className="w-5 h-5 text-green-600 dark:text-green-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                ) : (
                  <svg
                    className="w-5 h-5 text-yellow-600 dark:text-yellow-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                    />
                  </svg>
                )}
              </div>
              <div className="ml-3">
                <h4 className="font-medium">
                  {isAllFieldsCompleted
                    ? "Profil complet !"
                    : "Informations requises"}
                </h4>
                <p className="text-sm mt-1">
                  {isAllFieldsCompleted
                    ? "Toutes vos informations sont complètes. Vous pouvez maintenant continuer."
                    : "Veuillez remplir tous les champs requis pour continuer votre réservation."}
                </p>
              </div>
            </div>
          </div>

          <div className="w-full">
            <PersonalInfoForm
              currentUser={user}
              onSuccess={() => {
                // Trigger success message after form submission
                setShowSuccessMessage(true);
                // Also update onboarded status
                handleOnboarding();
              }}
            />
          </div>

          {/* Additional Save Button if needed */}
          {!isAllFieldsCompleted && (
            <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
              <p className="text-yellow-800 dark:text-yellow-200 text-sm mb-3">
                💡 N'oubliez pas de cliquer sur "Sauvegarder mes informations"
                après avoir rempli tous les champs requis.
              </p>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Show success message if user is onboarded or all fields are completed
  if (
    isAuthenticated &&
    (isOnboarded || showSuccessMessage || isAllFieldsCompleted)
  ) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-8 shadow-lg">
          <div className="text-center">
            <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg
                className="w-8 h-8 text-green-600 dark:text-green-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              Félicitations ! Votre profil est complet
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Vos informations personnelles ont été enregistrées avec succès.
              Vous pouvez maintenant continuer votre réservation.
            </p>
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6">
              <p className="text-green-800 dark:text-green-200 font-medium">
                ✅ Votre compte est maintenant activé et vous pouvez effectuer
                des réservations
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export default page;
