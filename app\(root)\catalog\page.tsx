"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import Title from "@/components/shared/Title";
import CarsList from "@/components/cars/cars-list";
import { Car as Voiture, Calendar, MapPin } from "lucide-react";
import CarsFilter from "./_components/cars-filter";
import { Car } from "@prisma/client";
import axios from "axios";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import {
  calculateBilling,
  formatBillingDisplay,
  getBillingExplanation,
} from "@/lib/billing";

interface FilterCriteria {
  category: string;
  fuelType: string;
  transmission: string;
}

interface ReservationDetails {
  startDate: Date | null;
  endDate: Date | null;
  startPlace: string;
  endPlace: string;
  days: number;
  totalHours: number;
  extraHours: number;
  toleranceApplied: boolean;
  billingExplanation: string[];
}

const Page = () => {
  const searchParams = useSearchParams();
  const [filteredCars, setFilteredCars] = useState<Car[]>([]);
  const [cars, setCars] = useState<Car[]>([]);
  const [filterCriteria, setFilterCriteria] = useState<FilterCriteria>({
    category: "",
    fuelType: "",
    transmission: "",
  });
  const [isFilterVisible, setIsFilterVisible] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [reservationDetails, setReservationDetails] =
    useState<ReservationDetails>({
      startDate: null,
      endDate: null,
      startPlace: "",
      endPlace: "",
      days: 0,
      totalHours: 0,
      extraHours: 0,
      toleranceApplied: false,
      billingExplanation: [],
    });

  // Extract reservation details from URL parameters
  useEffect(() => {
    const startDateParam = searchParams.get("startDate");
    const endDateParam = searchParams.get("endDate");
    const startPlaceParam = searchParams.get("startPlace");
    const endPlaceParam = searchParams.get("endPlace");
    const daysParam = searchParams.get("days");
    const totalHoursParam = searchParams.get("totalHours");
    const extraHoursParam = searchParams.get("extraHours");
    const toleranceAppliedParam = searchParams.get("toleranceApplied");

    if (startDateParam && endDateParam) {
      const startDate = new Date(startDateParam);
      const endDate = new Date(endDateParam);

      // Calculate billing details for explanation
      const billing = calculateBilling(startDate, endDate, 50); // Use dummy price
      const explanation = getBillingExplanation(billing);

      setReservationDetails({
        startDate,
        endDate,
        startPlace: startPlaceParam || "",
        endPlace: endPlaceParam || "",
        days: parseInt(daysParam || "1"),
        totalHours: parseInt(totalHoursParam || "0"),
        extraHours: parseInt(extraHoursParam || "0"),
        toleranceApplied: toleranceAppliedParam === "true",
        billingExplanation: explanation,
      });
    }
  }, [searchParams]);

  console.log("filteredCars", filteredCars);
  console.log("reservationDetails", reservationDetails);

  useEffect(() => {
    const fetchCars = async () => {
      try {
        setIsLoading(true);
        const carsData = await axios.get("/api/cars");
        setCars(carsData.data);
        setFilteredCars(carsData.data);
      } catch (error) {
        console.error("Error fetching cars:", error);
        setCars([]);
        setFilteredCars([]);
      } finally {
        setIsLoading(false);
      }
    };
    fetchCars();
  }, []);

  useEffect(() => {
    const applyFilter = () => {
      if (!Array.isArray(cars)) return;
      const filtered = cars.filter((car) => {
        const matchesCategory = filterCriteria.category
          ? car.category === filterCriteria.category
          : true;
        const matchesFuelType = filterCriteria.fuelType
          ? car.fuelType === filterCriteria.fuelType
          : true;
        const matchesTransmission = filterCriteria.transmission
          ? car.transmission === filterCriteria.transmission
          : true;

        return matchesCategory && matchesFuelType && matchesTransmission;
      });
      setFilteredCars(filtered);
    };
    applyFilter();
  }, [filterCriteria, cars]);

  const handleFilter = (criteria: FilterCriteria) => {
    setFilterCriteria(criteria);
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="py-8">
      <Title
        title="Découvrez Notre Catalogue de Voitures"
        description="Explorez notre vaste sélection de voitures disponibles à la location. Que vous ayez besoin d'une voiture pour un voyage d'affaires, une escapade de week-end ou un déplacement quotidien, nous avons le véhicule parfait pour vous."
        icon={Voiture}
      />

      {/* Reservation Summary */}
      {reservationDetails.startDate && reservationDetails.endDate && (
        <div className="mt-8 p-6 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 bg-yellow-500 rounded-lg">
              <Calendar className="w-5 h-5 text-white" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-white">
              Détails de votre réservation
            </h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <Calendar className="w-5 h-5 text-yellow-600" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Date de départ
                </p>
                <p className="font-semibold text-gray-900 dark:text-white">
                  {format(reservationDetails.startDate, "dd MMM yyyy", {
                    locale: fr,
                  })}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <Calendar className="w-5 h-5 text-orange-600" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Date de retour
                </p>
                <p className="font-semibold text-gray-900 dark:text-white">
                  {format(reservationDetails.endDate, "dd MMM yyyy", {
                    locale: fr,
                  })}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="w-5 h-5 bg-blue-600 rounded flex items-center justify-center">
                <span className="text-white text-xs font-bold">
                  {reservationDetails.days}
                </span>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Durée
                </p>
                <p className="font-semibold text-gray-900 dark:text-white">
                  {reservationDetails.days} jour
                  {reservationDetails.days > 1 ? "s" : ""}
                </p>
              </div>
            </div>

            {(reservationDetails.startPlace || reservationDetails.endPlace) && (
              <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <MapPin className="w-5 h-5 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Lieux
                  </p>
                  <p className="font-semibold text-gray-900 dark:text-white text-sm">
                    {reservationDetails.startPlace} →{" "}
                    {reservationDetails.endPlace}
                  </p>
                </div>
              </div>
            )}
          </div>

          <div className="mt-6 space-y-4">
            <div className="p-3 bg-yellow-100 dark:bg-gray-600 rounded-lg">
              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                💡 Les prix affichés sont calculés pour{" "}
                {reservationDetails.days} jour
                {reservationDetails.days > 1 ? "s" : ""} de location
                {reservationDetails.toleranceApplied &&
                  " (tolérance de 2h appliquée)"}
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="flex max-lg:flex-col gap-8 mt-12">
        {/* Filter Toggle Button for Mobile */}
        <button
          className="lg:hidden bg-yellow-400 text-black px-4 py-2 rounded-lg font-semibold hover:bg-yellow-500 transition-colors"
          onClick={() => setIsFilterVisible(!isFilterVisible)}
        >
          {isFilterVisible ? "Cacher le Filtre" : "Afficher le Filtre"}
        </button>

        {/* Filters Section */}
        <div
          className={`${
            isFilterVisible ? "block" : "hidden"
          } lg:block lg:w-1/4 w-full flex gap-8 max-lg:flex-col`}
        >
          <CarsFilter onFilter={handleFilter} />
        </div>

        {/* Car List Section */}
        <div className="flex-1">
          <CarsList
            cars={filteredCars}
            reservationDetails={reservationDetails}
          />
        </div>
      </div>
    </div>
  );
};

export default Page;
