@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: "Inter", sans-serif;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 20 14.3% 4.1%;
    --card: 0 0% 100%;
    --card-foreground: 20 14.3% 4.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 20 14.3% 4.1%;
    --primary: 47.9 95.8% 53.1%; /* Keep yellow as primary */
    --primary-foreground: 26 83.3% 14.1%;
    --secondary: 210 40% 98%; /* Light blue-gray */
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 60 9.1% 97.8%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 47.9 95.8% 53.1%; /* Yellow ring */
    --radius: 0.75rem; /* Slightly more rounded */
    --chart-1: 47.9 95.8% 53.1%; /* Yellow */
    --chart-2: 142.1 76.2% 36.3%; /* Green */
    --chart-3: 346.8 77.2% 49.8%; /* Red */
    --chart-4: 221.2 83.2% 53.3%; /* Blue */
    --chart-5: 262.1 83.3% 57.8%; /* Purple */

    /* Custom color variables for enhanced palette */
    --orange: 24.6 95% 53.1%;
    --orange-foreground: 60 9.1% 97.8%;
    --green: 142.1 76.2% 36.3%;
    --green-foreground: 355.7 100% 97.3%;
    --blue: 221.2 83.2% 53.3%;
    --blue-foreground: 210 40% 98%;
    --purple: 262.1 83.3% 57.8%;
    --purple-foreground: 210 40% 98%;
    --gradient-start: 47.9 95.8% 53.1%; /* Yellow */
    --gradient-end: 24.6 95% 53.1%; /* Orange */
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 47.9 95.8% 53.1%; /* Keep yellow as primary */
    --primary-foreground: 26 83.3% 14.1%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 47.9 95.8% 53.1%; /* Yellow ring */
    --chart-1: 47.9 95.8% 53.1%; /* Yellow */
    --chart-2: 142.1 76.2% 36.3%; /* Green */
    --chart-3: 346.8 77.2% 49.8%; /* Red */
    --chart-4: 221.2 83.2% 53.3%; /* Blue */
    --chart-5: 262.1 83.3% 57.8%; /* Purple */

    /* Custom color variables for enhanced palette - dark mode */
    --orange: 24.6 95% 53.1%;
    --orange-foreground: 222.2 84% 4.9%;
    --green: 142.1 76.2% 36.3%;
    --green-foreground: 222.2 84% 4.9%;
    --blue: 221.2 83.2% 53.3%;
    --blue-foreground: 222.2 84% 4.9%;
    --purple: 262.1 83.3% 57.8%;
    --purple-foreground: 222.2 84% 4.9%;
    --gradient-start: 47.9 95.8% 53.1%; /* Yellow */
    --gradient-end: 24.6 95% 53.1%; /* Orange */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  .border-color {
    @apply border-gray-200;
  }
}

@layer utilities {
  /* Custom color utilities */
  .bg-orange {
    background-color: hsl(var(--orange));
  }
  .text-orange {
    color: hsl(var(--orange));
  }
  .bg-green {
    background-color: hsl(var(--green));
  }
  .text-green {
    color: hsl(var(--green));
  }
  .bg-blue {
    background-color: hsl(var(--blue));
  }
  .text-blue {
    color: hsl(var(--blue));
  }
  .bg-purple {
    background-color: hsl(var(--purple));
  }
  .text-purple {
    color: hsl(var(--purple));
  }

  /* Gradient utilities */
  .bg-gradient-primary {
    background: linear-gradient(
      135deg,
      hsl(var(--gradient-start)),
      hsl(var(--gradient-end))
    );
  }
  .bg-gradient-hero {
    background: linear-gradient(
      135deg,
      hsl(var(--primary)) 0%,
      hsl(var(--orange)) 50%,
      hsl(var(--blue)) 100%
    );
  }
  .bg-gradient-card {
    background: linear-gradient(
      145deg,
      hsl(var(--card)) 0%,
      hsl(var(--secondary)) 100%
    );
  }

  /* Enhanced shadows */
  .shadow-glow {
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
  }
  .shadow-glow-blue {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  .shadow-glow-green {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
  }
}
