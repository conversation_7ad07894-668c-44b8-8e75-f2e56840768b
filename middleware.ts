import NextAuth from "next-auth";
import authConfig from "@/auth.config";
import {
  DEFAULT_LOGIN_REDIRECT,
  apiAuthPrefix,
  authRoutes,
  publicRoutes,
} from "@/routes";
import { NextResponse } from "next/server";
import { useCurrentUser } from "./hooks/use-current-user";

const { auth } = NextAuth(authConfig);

export default auth((req: any): void | Response | Promise<void | Response> => {
  const { nextUrl } = req;
  const isLoggedIn = !!req.auth;
  const userRole = req.auth?.user?.role;
  const isOnboarded = req.auth?.user?.isOnboarded;

  const isApiAuthRoute = nextUrl.pathname.startsWith(apiAuthPrefix);
  const isPublicRoute = publicRoutes.includes(nextUrl.pathname);
  const isAuthRoute = authRoutes.includes(nextUrl.pathname);
  const isOnboardingRoute = nextUrl.pathname === "/onboarding";

  if (isApiAuthRoute) {
    return;
  }

  if (isAuthRoute) {
    if (isLoggedIn) {
      // If user is logged in but not onboarded, redirect to onboarding
      if (!isOnboarded && !isOnboardingRoute) {
        return NextResponse.redirect(new URL("/onboarding", nextUrl));
      }
      // If user is onboarded and trying to access auth routes, redirect to dashboard
      if (isOnboarded) {
        return NextResponse.redirect(new URL(DEFAULT_LOGIN_REDIRECT, nextUrl));
      }
    }
    return;
  }

  if (!isLoggedIn && !isPublicRoute) {
    return NextResponse.redirect(new URL("/sign-in", nextUrl));
  }

  // If user is logged in but not onboarded, redirect to onboarding (except for onboarding page itself)
  if (isLoggedIn && !isOnboarded && !isOnboardingRoute && !isPublicRoute) {
    return NextResponse.redirect(new URL("/onboarding", nextUrl));
  }

  // If user is onboarded but trying to access onboarding page, redirect to dashboard
  if (isLoggedIn && isOnboarded && isOnboardingRoute) {
    return NextResponse.redirect(new URL("/user/reservations", nextUrl));
  }

  // Protect admin routes
  // if (nextUrl.pathname.startsWith("/admin") && userRole !== "ADMIN") {
  //   return NextResponse.redirect(new URL("/user/reservations", nextUrl));
  // }

  return;
});

export const config = {
  matcher: ["/((?!.+\\.[\\w]+$|_next).*)", "/", "/(api|trpc)(.*)"],
};
