{"name": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "prisma": {"seed": "ts-node ./scripts/seed.ts"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "vercel-build": "next build && prisma generate && prisma db seed && prisma migrate deploy ", "postinstall": "prisma generate"}, "dependencies": {"@aarkue/tiptap-math-extension": "^1.3.3", "@auth/core": "^0.37.4", "@auth/prisma-adapter": "^2.7.4", "@blocknote/core": "^0.18.0", "@blocknote/mantine": "^0.18.1", "@blocknote/react": "^0.18.1", "@clerk/nextjs": "^4.27.2", "@google-cloud/storage": "^7.7.0", "@headlessui/react": "^2.2.0", "@hello-pangea/dnd": "^16.6.0", "@hookform/resolvers": "^3.9.1", "@liveblocks/client": "^1.10.2", "@liveblocks/node": "^1.10.2", "@liveblocks/react": "^1.10.2", "@liveblocks/yjs": "^1.10.2", "@mantine/core": "^7.13.4", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@mux/mux-node": "^8.8.0", "@mux/mux-player-react": "^2.9.1", "@next/font": "^14.0.2", "@next/mdx": "^14.2.18", "@prisma/client": "^5.22.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.4", "@react-pdf/renderer": "^4.1.6", "@stream-io/video-react-sdk": "^1.7.11", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.3.0", "@supabase/supabase-js": "^2.43.4", "@tanstack/react-table": "^8.20.5", "@tiptap/extension-collaboration": "^2.2.4", "@tiptap/extension-collaboration-cursor": "^2.2.4", "@tiptap/pm": "^2.10.2", "@tiptap/react": "^2.10.2", "@tiptap/starter-kit": "^2.10.2", "@tsparticles/engine": "^3.7.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.7.1", "@types/mdx": "^2.0.13", "@uploadthing/react": "^6.6.0", "@vercel/analytics": "^1.4.0", "aws4": "^1.12.0", "axios": "^1.7.7", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "better-react-mathjax": "^2.0.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "cors": "^2.8.5", "date-fns": "^4.1.0", "diff": "^7.0.0", "dropbox": "^10.34.0", "embla-carousel-react": "^8.0.0-rc17", "express": "^4.18.2", "framer-motion": "^11.11.17", "html-to-latex": "^0.8.0", "html-to-md": "^0.8.6", "input-otp": "^1.4.1", "jsonwebtoken": "^9.0.2", "jspdf": "^2.5.2", "katex": "^0.16.11", "lowlight": "^3.2.0", "lucide-react": "^0.460.0", "mdx-bundler": "^10.0.3", "mini-svg-data-uri": "^1.4.4", "moment": "^2.30.1", "mongodb": "^6.3.0", "mongoose": "^8.0.2", "next": "^14.0.4", "next-auth": "^5.0.0-beta.25", "next-themes": "^0.2.1", "novel": "^0.5.0", "posthog-js": "^1.184.2", "query-string": "^9.1.0", "react": "^18", "react-confetti": "^6.1.0", "react-day-picker": "8.10.1", "react-dom": "^18", "react-hook-form": "^7.53.2", "react-hot-toast": "^2.4.1", "react-icons": "^5.3.0", "react-katex": "^3.0.1", "react-latex": "^1.2.0", "react-latex-next": "^3.0.0", "react-markdown": "^9.0.1", "react-quill": "^2.0.0", "react-slick": "^0.29.0", "react-textarea-autosize": "^8.5.4", "recharts": "^2.13.3", "rehype-autolink-headings": "^7.1.0", "rehype-katex": "^7.0.0", "rehype-parse": "^9.0.1", "rehype-pretty-code": "^0.13.1", "rehype-raw": "^7.0.0", "rehype-react": "^8.0.0", "rehype-remark": "^10.0.0", "rehype-slug": "^6.0.0", "remark-math": "^6.0.0", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.1", "remark-stringify": "^11.0.0", "resend": "^3.3.0", "shiki": "^1.16.2", "slick-carousel": "^1.8.1", "sonner": "^1.7.0", "stripe": "^17.2.0", "supabase": "^1.169.8", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "turndown": "^7.2.0", "unified": "^11.0.5", "uploadthing": "^6.12.0", "uuid": "^10.0.0", "vaul": "^1.1.1", "vercel": "^39.0.1", "y-prosemirror": "^1.2.3", "yjs": "^13.6.14", "zod": "^3.23.8", "zustand": "^5.0.0-rc.2"}, "devDependencies": {"@tailwindcss/typography": "^0.5.15", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/node": "^22.7.7", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-katex": "^3.0.4", "@types/turndown": "^5.0.5", "@types/uuid": "^9.0.8", "autoprefixer": "^10.0.1", "postcss": "^8", "prisma": "^5.22.0", "tailwindcss": "^3.3.0", "ts-node": "^10.9.2", "typescript": "^5.6.3", "velite": "^0.1.0-beta.14"}}