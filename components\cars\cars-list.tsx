import React from "react";
import CarCard from "./car-card";
import { Car } from "@prisma/client";
import { useRouter } from "next/navigation";
import axios from "axios";
import { calculateBilling } from "@/lib/billing";

interface ReservationDetails {
  startDate: Date | null;
  endDate: Date | null;
  startPlace: string;
  endPlace: string;
  days: number;
}

interface CarsListProps {
  cars: Car[];
  reservationDetails?: ReservationDetails;
}

const CarsList = ({ cars, reservationDetails }: CarsListProps) => {
  const router = useRouter();

  const calculateTotalPrice = (pricePerDay: number) => {
    if (
      !reservationDetails ||
      !reservationDetails.startDate ||
      !reservationDetails.endDate
    ) {
      return pricePerDay;
    }

    // Use the advanced billing system
    const billing = calculateBilling(
      reservationDetails.startDate,
      reservationDetails.endDate,
      pricePerDay
    );

    return billing.totalPrice;
  };

  if (cars === null) {
    return (
      <div className="flex justify-center items-center h-full">
        <p className="text-center text-gray-500 text-lg">Loading cars...</p>
      </div>
    );
  }

  if (cars.length === 0) {
    return (
      <div className="flex justify-center items-center h-full">
        <p className="text-center text-gray-500 text-lg">
          No cars available at the moment.
        </p>
      </div>
    );
  }

  const handleReserveCar = async (car: Car) => {
    try {
      // If we have reservation details, create reservation with dates (from homepage)
      if (
        reservationDetails &&
        reservationDetails.startDate &&
        reservationDetails.endDate
      ) {
        const response = await axios.post("/api/reservations", {
          carId: car.id,
          startDate: reservationDetails.startDate,
          endDate: reservationDetails.endDate,
          startPlace: reservationDetails.startPlace,
          endPlace: reservationDetails.endPlace,
        });
        const reservationId = response.data.id;
        router.push(
          `/user/reservations/${reservationId}?selectedCar=${car.id}&fromHomepage=true`
        );
      } else {
        // For catalog reservations (no pre-filled dates)
        const response = await axios.post("/api/reservations", {
          carId: car.id,
        });
        const reservationId = response.data.id;
        router.push(
          `/user/reservations/${reservationId}?selectedCar=${car.id}&fromHomepage=false`
        );
      }
    } catch (error) {
      console.error("Error creating reservation:", error);
    }
  };

  return (
    <div className="w-full grid grid-cols-3 max-md:grid-cols-1 max-xl:grid-cols-2 gap-6">
      {cars.map((car: Car) => (
        <CarCard
          key={car.id}
          id={car.id}
          name={car.name || "Unnamed Car"}
          model={car.model || "Unknown Model"}
          pricePerDay={car.pricePerDay || 0}
          totalPrice={calculateTotalPrice(car.pricePerDay || 0)}
          days={reservationDetails?.days || 1}
          seats={car.seats || 0}
          fuelType={car.fuelType || "N/A"}
          category={car.category || "N/A"}
          transmission={car.transmission || "N/A"}
          imageUrl={car.imageUrl || "/default-image.png"}
          description={car.description || "No description available"}
          onReserve={() => handleReserveCar(car)}
        />
      ))}
    </div>
  );
};

export default CarsList;
