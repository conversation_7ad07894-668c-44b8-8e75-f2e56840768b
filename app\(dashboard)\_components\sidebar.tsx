import React from "react";
import Logo from "./logo";
import { SidebarRoutes } from "./sidebar-routes";
import AccountCard from "@/components/auth/account-card";
import { ArrowBigLeft, ArrowLeft, Home } from "lucide-react";
import Link from "next/link";
import { ModeToggle } from "@/components/shared/ThemeSwitcher";
import { Button } from "@/components/ui/button";

const sidebar = () => {
  return (
    <div className="h-full bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col overflow-y-auto shadow-lg">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <Link href={"/"} className="block">
          <Logo />
        </Link>
      </div>

      {/* Navigation */}
      <div className="flex-1 px-3 py-4">
        <div className="mb-6">
          <Link href="/">
            <Button
              variant="ghost"
              className="w-full justify-start gap-3 text-gray-600 hover:text-yellow-600 hover:bg-yellow-50 dark:text-gray-300 dark:hover:text-yellow-400 dark:hover:bg-yellow-900/20"
            >
              <Home className="h-5 w-5" />
              Retour à l'accueil
            </Button>
          </Link>
        </div>

        <div className="space-y-2">
          <SidebarRoutes />
        </div>
      </div>

      {/* Footer */}
      <div className="p-3 border-t border-gray-200 dark:border-gray-700 space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600 dark:text-gray-400">
            Thème
          </span>
          <ModeToggle />
        </div>
        <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
          © 2024 Bendriouchcar
        </div>
      </div>
    </div>
  );
};

export default sidebar;
