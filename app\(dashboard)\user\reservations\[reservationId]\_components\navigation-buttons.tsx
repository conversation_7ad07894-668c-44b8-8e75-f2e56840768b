import { Button } from "@/components/ui/button";
import { useCurrentUser } from "@/hooks/use-current-user";

interface NavigationButtonsProps {
  currentStep: number;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  selectedCar: any; // You can refine this type as needed
}

const NavigationButtons = ({
  currentStep,
  goToNextStep,
  goToPreviousStep,
  selectedCar,
}: NavigationButtonsProps) => {
  const user = useCurrentUser();
  const isOnboarded = user?.isOnboarded ?? false;

  return (
    <div className="mt-8 flex justify-between w-full">
      {currentStep > 1 && (
        <Button variant="outline" onClick={goToPreviousStep}>
          Précédent
        </Button>
      )}
      {currentStep === 2 && isOnboarded && (
        <Button
          variant="default"
          onClick={goToNextStep}
          className="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white font-semibold"
        >
          Continuer
        </Button>
      )}
    </div>
  );
};

export default NavigationButtons;
