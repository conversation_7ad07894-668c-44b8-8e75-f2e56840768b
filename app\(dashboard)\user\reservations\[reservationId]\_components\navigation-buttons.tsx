import { Button } from "@/components/ui/button";
import { useCurrentUser } from "@/hooks/use-current-user";

interface NavigationButtonsProps {
  currentStep: number;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  selectedCar: any; // You can refine this type as needed
  showContinueButton?: boolean;
}

const NavigationButtons = ({
  currentStep,
  goToNextStep,
  goToPreviousStep,
  selectedCar,
  showContinueButton = false,
}: NavigationButtonsProps) => {
  const user = useCurrentUser();
  const isOnboarded = user?.isOnboarded ?? false;

  // Check if all required fields are completed
  const isAllFieldsCompleted =
    user?.firstName &&
    user?.lastName &&
    user?.phone &&
    user?.city &&
    user?.country &&
    user?.birthday;

  const shouldShowContinueButton =
    currentStep === 2 &&
    (isOnboarded || isAllFieldsCompleted || showContinueButton);

  return (
    <div className="mt-8 flex justify-between w-full">
      {currentStep > 1 && (
        <Button variant="outline" onClick={goToPreviousStep}>
          Précédent
        </Button>
      )}
      {shouldShowContinueButton && (
        <Button
          variant="default"
          onClick={() => {
            console.log("Continue button clicked, going to next step");
            goToNextStep();
          }}
          className="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white font-semibold"
        >
          Continuer
        </Button>
      )}
    </div>
  );
};

export default NavigationButtons;
