import { Button } from "@/components/ui/button";

interface NavigationButtonsProps {
  currentStep: number;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  selectedCar: any; // You can refine this type as needed
}

const NavigationButtons = ({
  currentStep,
  goToNextStep,
  goToPreviousStep,
  selectedCar,
}: NavigationButtonsProps) => {
  // No special logic needed since step 2 is removed

  return (
    <div className="mt-8 flex justify-between w-full">
      {currentStep > 1 && (
        <Button variant="outline" onClick={goToPreviousStep}>
          Précédent
        </Button>
      )}
    </div>
  );
};

export default NavigationButtons;
