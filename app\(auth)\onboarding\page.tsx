"use client";

import { useCurrentUser } from "@/hooks/use-current-user";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import PersonalInfoForm from "@/app/(dashboard)/user/settings/_components/personal-info-form";
import axios from "axios";
import toast from "react-hot-toast";

const OnboardingPage = () => {
  const user = useCurrentUser();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isAuthenticated = user !== null;
  const isOnboarded = user?.isOnboarded ?? false;

  const isAllFieldsCompleted =
    user?.firstName &&
    user?.lastName &&
    user?.phone &&
    user?.city &&
    user?.country &&
    user?.birthday;

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/sign-in");
    }
  }, [isAuthenticated, router]);

  // Redirect if already onboarded
  useEffect(() => {
    if (isAuthenticated && isOnboarded) {
      router.push("/user/reservations");
    }
  }, [isAuthenticated, isOnboarded, router]);

  // Function to handle the onboarding completion
  const handleCompleteOnboarding = async () => {
    if (!isAllFieldsCompleted) {
      toast.error("Veuillez remplir tous les champs requis avant de continuer");
      return;
    }

    setIsSubmitting(true);

    try {
      console.log("Completing onboarding...");
      await axios.patch("/api/user/onboard", {
        isOnboarded: true,
      });

      console.log("Onboarding completed successfully");
      toast.success("Bienvenue ! Votre compte est maintenant configuré.");
      router.push("/user/reservations");
    } catch (error) {
      console.error("Failed to complete onboarding:", error);
      toast.error("Erreur lors de la finalisation de votre compte");
      setIsSubmitting(false);
    }
  };

  if (!isAuthenticated) {
    return <div>Redirection...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl w-full">
        <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-8 shadow-lg">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              Bienvenue chez Bendriouch Cars !
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              Pour commencer à utiliser notre service, nous avons besoin de
              quelques informations personnelles.
            </p>
          </div>

          {/* Progress indicator */}
          <div className="mb-8">
            <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-2">
              <span>Configuration de votre compte</span>
              <span>{isAllFieldsCompleted ? "Complet" : "En cours..."}</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  isAllFieldsCompleted
                    ? "bg-green-500 w-full"
                    : "bg-yellow-500 w-3/4"
                }`}
              />
            </div>
          </div>

          {/* Alert Message */}
          <div
            className={`p-4 rounded-lg border mb-6 ${
              isAllFieldsCompleted
                ? "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-200"
                : "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200"
            }`}
          >
            <div className="flex items-start">
              <div className="flex-shrink-0">
                {isAllFieldsCompleted ? (
                  <svg
                    className="w-5 h-5 text-green-600 dark:text-green-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                ) : (
                  <svg
                    className="w-5 h-5 text-blue-600 dark:text-blue-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                )}
              </div>
              <div className="ml-3">
                <h4 className="font-medium">
                  {isAllFieldsCompleted
                    ? "Informations complètes !"
                    : "Informations requises"}
                </h4>
                <p className="text-sm mt-1">
                  {isAllFieldsCompleted
                    ? "Toutes vos informations sont complètes. Vous pouvez maintenant accéder à votre compte."
                    : "Veuillez remplir tous les champs requis pour activer votre compte."}
                </p>
              </div>
            </div>
          </div>

          {/* Form */}
          <div className="w-full">
            <PersonalInfoForm
              currentUser={user}
              onSuccess={() => {
                console.log("Form saved successfully, refreshing user data");
                router.refresh();
              }}
            />
          </div>

          {/* Complete Onboarding Button */}
          <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              disabled={!isAllFieldsCompleted || isSubmitting}
              onClick={handleCompleteOnboarding}
              className={`w-full h-12 rounded-lg font-semibold transition-all duration-300 ${
                isAllFieldsCompleted && !isSubmitting
                  ? "bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg hover:shadow-xl"
                  : "bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed"
              }`}
            >
              {isSubmitting ? (
                <div className="flex items-center justify-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Finalisation...
                </div>
              ) : (
                "Activer mon compte et commencer"
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OnboardingPage;
