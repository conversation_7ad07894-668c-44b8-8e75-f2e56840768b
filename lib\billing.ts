import { differenceInHours, differenceInDays, parseISO } from "date-fns";

export interface BillingCalculation {
  totalDays: number;
  totalHours: number;
  billableDays: number;
  extraHours: number;
  toleranceApplied: boolean;
  pricePerDay: number;
  totalPrice: number;
  breakdown: {
    baseDays: number;
    baseDaysPrice: number;
    extraDaysFromHours: number;
    extraDaysPrice: number;
    totalPrice: number;
  };
  isValidReservation: boolean;
  validationErrors: string[];
}

/**
 * Calculate billing for car rental with advanced rules:
 * - Minimum 3 days rental
 * - 2-hour tolerance (if extra hours <= 2, don't charge extra day)
 * - Any extra hours > 2 are charged as full day
 * - 24h = 1 day billing cycle
 */
export function calculateBilling(
  startDate: Date | string,
  endDate: Date | string,
  pricePerDay: number
): BillingCalculation {
  const start = typeof startDate === 'string' ? parseISO(startDate) : startDate;
  const end = typeof endDate === 'string' ? parseISO(endDate) : endDate;
  
  const validationErrors: string[] = [];
  
  // Validate dates
  if (start >= end) {
    validationErrors.push("La date de fin doit être après la date de début");
  }
  
  if (pricePerDay <= 0) {
    validationErrors.push("Le prix par jour doit être positif");
  }
  
  // Calculate total time difference
  const totalHours = differenceInHours(end, start);
  const totalDays = Math.floor(totalHours / 24);
  const extraHours = totalHours % 24;
  
  // Check minimum 3 days requirement
  if (totalDays < 3) {
    validationErrors.push("La durée minimum de location est de 3 jours");
  }
  
  // Apply tolerance rule (2 hours max tolerance)
  const TOLERANCE_HOURS = 2;
  let billableDays = totalDays;
  let toleranceApplied = false;
  
  if (extraHours > 0) {
    if (extraHours <= TOLERANCE_HOURS) {
      // Within tolerance, don't charge extra day
      toleranceApplied = true;
    } else {
      // Beyond tolerance, charge full extra day
      billableDays += 1;
    }
  }
  
  // Ensure minimum 3 days billing
  const finalBillableDays = Math.max(billableDays, 3);
  
  // Calculate pricing breakdown
  const baseDays = Math.min(finalBillableDays, totalDays);
  const extraDaysFromHours = finalBillableDays - baseDays;
  
  const baseDaysPrice = baseDays * pricePerDay;
  const extraDaysPrice = extraDaysFromHours * pricePerDay;
  const totalPrice = baseDaysPrice + extraDaysPrice;
  
  return {
    totalDays,
    totalHours,
    billableDays: finalBillableDays,
    extraHours,
    toleranceApplied,
    pricePerDay,
    totalPrice,
    breakdown: {
      baseDays,
      baseDaysPrice,
      extraDaysFromHours,
      extraDaysPrice,
      totalPrice,
    },
    isValidReservation: validationErrors.length === 0,
    validationErrors,
  };
}

/**
 * Format billing calculation for display
 */
export function formatBillingDisplay(calculation: BillingCalculation): string {
  const { totalDays, extraHours, billableDays, toleranceApplied, totalPrice } = calculation;
  
  let display = `${totalDays} jour${totalDays > 1 ? 's' : ''}`;
  
  if (extraHours > 0) {
    display += ` et ${extraHours}h`;
    
    if (toleranceApplied) {
      display += ` (tolérance appliquée)`;
    } else {
      display += ` (facturé comme jour supplémentaire)`;
    }
  }
  
  display += ` = ${billableDays} jour${billableDays > 1 ? 's' : ''} facturé${billableDays > 1 ? 's' : ''}`;
  display += ` = ${totalPrice}€`;
  
  return display;
}

/**
 * Get detailed billing explanation
 */
export function getBillingExplanation(calculation: BillingCalculation): string[] {
  const explanations: string[] = [];
  const { totalDays, extraHours, billableDays, toleranceApplied, breakdown } = calculation;
  
  explanations.push(`Durée totale: ${totalDays} jour${totalDays > 1 ? 's' : ''} et ${extraHours}h`);
  
  if (extraHours > 0) {
    if (toleranceApplied) {
      explanations.push(`✅ Tolérance de 2h appliquée (${extraHours}h ≤ 2h)`);
    } else {
      explanations.push(`⚠️ Heures supplémentaires facturées (${extraHours}h > 2h = +1 jour)`);
    }
  }
  
  if (billableDays < 3) {
    explanations.push(`📋 Minimum 3 jours appliqué (${billableDays} → 3 jours)`);
  }
  
  explanations.push(`💰 Facturation: ${breakdown.baseDays} jour${breakdown.baseDays > 1 ? 's' : ''} × ${calculation.pricePerDay}€ = ${breakdown.baseDaysPrice}€`);
  
  if (breakdown.extraDaysFromHours > 0) {
    explanations.push(`💰 Jours supplémentaires: ${breakdown.extraDaysFromHours} × ${calculation.pricePerDay}€ = ${breakdown.extraDaysPrice}€`);
  }
  
  explanations.push(`🎯 Total: ${calculation.totalPrice}€`);
  
  return explanations;
}

/**
 * Validate reservation dates and return errors if any
 */
export function validateReservationDates(
  startDate: Date | string,
  endDate: Date | string
): { isValid: boolean; errors: string[] } {
  const calculation = calculateBilling(startDate, endDate, 1); // Use dummy price for validation
  return {
    isValid: calculation.isValidReservation,
    errors: calculation.validationErrors,
  };
}
