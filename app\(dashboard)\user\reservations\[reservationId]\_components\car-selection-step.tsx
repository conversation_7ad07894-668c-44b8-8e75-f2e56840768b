"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Car, Reservation } from "@prisma/client";
import { UnifiedReservationForm } from "./unified-reservation-form";
import { useEffect, useState } from "react";
import axios from "axios";
import CarSelection from "@/components/cars/car-selection";

interface CarSelectionStepProps {
  cars: Car[];
  selectedCar?: Car;
  handleCarSelect: (car: any) => void;
  reservation: Reservation;
  reservationId: string;
  goToNextStep: () => void;
}

const CarSelectionStep = ({
  cars,
  selectedCar,
  handleCarSelect,
  reservation,
  reservationId,
  goToNextStep,
}: CarSelectionStepProps) => {
  const [isNextButtonActive, setIsNextButtonActive] = useState(false);

  useEffect(() => {
    const fetchReservation = async () => {
      try {
        const response = await axios.get(`/api/reservations/${reservationId}`);
        const reservationData = response.data;

        if (reservationData.carId) {
          const car = cars.find((car) => car.id === reservationData.carId);
          if (car) {
            handleCarSelect(car);
            setIsNextButtonActive(true);
          }
        }
      } catch (error) {
        console.error("Error fetching reservation data:", error);
      }
    };

    fetchReservation();
  }, [reservationId, cars, handleCarSelect]);

  useEffect(() => {
    if (selectedCar) {
      setIsNextButtonActive(true);
    } else {
      setIsNextButtonActive(false);
    }
  }, [selectedCar]);

  const handleCarClick = (car: Car) => {
    if (selectedCar?.id === car.id) {
      handleCarSelect(undefined); // Deselect the car
      setIsNextButtonActive(false);
    } else {
      handleCarSelect(car); // Select the car
      setIsNextButtonActive(true);
      // Automatically proceed to next step after a short delay
      setTimeout(() => {
        goToNextStep();
      }, 500);
    }
  };

  return (
    <div className="space-y-8">
      {/* Unified Reservation Form */}
      <UnifiedReservationForm
        initialData={reservation}
        reservationId={reservationId}
      />

      {/* Car Selection Section */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-8 shadow-lg">
        <div className="mb-6">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Choisissez votre véhicule
          </h3>
          <p className="text-gray-600 dark:text-gray-300">
            Sélectionnez le véhicule qui correspond le mieux à vos besoins
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {cars.map((car) => (
            <CarSelection
              key={car.id}
              name={car.name || "Non spécifié"}
              model={car.model || "Non spécifié"}
              pricePerDay={car.pricePerDay || 0}
              fuelType={car.fuelType || "Non spécifié"}
              seats={car.seats || 0}
              transmission={car.transmission || "Non spécifié"}
              imageUrl={car.imageUrl[0] || "/placeholder-car.png"}
              selectedCar={selectedCar}
              onReserve={() => handleCarClick(car)}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default CarSelectionStep;
