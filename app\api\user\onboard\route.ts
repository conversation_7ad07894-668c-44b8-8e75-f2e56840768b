import { NextRequest, NextResponse } from "next/server";
import { currentUser } from "@/lib/auth";
import { db } from "@/lib/db";

export async function PATCH(req: NextRequest) {
  try {
    const user = await currentUser();

    if (!user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { isOnboarded } = await req.json();

    // Update user onboarded status
    const updatedUser = await db.user.update({
      where: {
        id: user.id,
      },
      data: {
        isOnboarded: isOnboarded,
      },
    });

    return NextResponse.json(updatedUser);
  } catch (error) {
    console.log("[USER_ONBOARD_PATCH]", error);
    return new NextResponse("Internal Error", { status: 500 });
  }
}
