import React from "react";

interface DashboardTitleProps {
  title: string;
  description: string;
}

const DashboardPageTitle = ({ title, description }: DashboardTitleProps) => {
  return (
    <div className="mb-6">
      <div className="flex flex-col items-start gap-2">
        <h1 className="text-gray-900 dark:text-white text-3xl lg:text-4xl font-bold tracking-tight">
          {title}
        </h1>
        <p className="text-gray-600 dark:text-gray-300 text-base leading-relaxed max-w-3xl">
          {description}
        </p>
      </div>
      <div className="mt-4 h-1 w-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full"></div>
    </div>
  );
};

export default DashboardPageTitle;
