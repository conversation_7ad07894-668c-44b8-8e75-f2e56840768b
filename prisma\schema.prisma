datasource db {
  provider  = "postgresql"
  url  	    = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
  seed    = "../scripts/seed.ts"
}


enum UserRole {
  ADMIN
  USER
}

model Account {
  id                 String  @id @default(cuid())
  userId             String  @map("user_id")
  type               String
  provider           String
  providerAccountId  String  @map("provider_account_id")
  refresh_token      String? @db.Text
  access_token       String? @db.Text
  expires_at         Int?
  token_type         String?
  scope              String?
  id_token           String? @db.Text
  session_state      String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model User {
  id                    String        @id @default(cuid())
  firstName             String?
  lastName              String?
  password              String?
  phone                 String?
  image                 String?
  permis                String?
  passport              String?
  city                  String?
  country               String?
  birthday              DateTime?
  email                 String?       @unique
  emailVerified         DateTime?
  isOnboarded           <PERSON>olean       @default(false)
  role                  UserRole      @default(USER)
  accounts              Account[]
  reservations          Reservation[]
  isTwoFactorEnabled    Boolean       @default(false)
  twoFactorConfirmation TwoFactorConfirmation?
}

model VerificationToken {
  id            String        @id @default(cuid())
  email         String
  token         String        @unique
  expires       DateTime
  @@unique([email, token])
}

model PasswordResetToken {
  id            String        @id @default(cuid())
  email         String
  token         String        @unique
  expires       DateTime

  @@unique([email, token])
}

model TwoFactorToken {
  id            String        @id @default(cuid())
  email         String
  token         String        @unique
  expires       DateTime

  @@unique([email, token])
}

model TwoFactorConfirmation {
  id            String              @id @default(cuid())
  userId        String
  user          User                @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId])
}

// USER

// ENUM 

// VOITURE

model Car {
  id                String          @id @default(cuid()) 
  name              String?  
  model             String? 
  pricePerDay       Float?  
  imageUrl          String[]
  description       String?
  fuelType          String?   
  seats             Float?      
  transmission      String?
  category          CarCategory?   
  isPublished       Boolean         @default(false)
  createdAt         DateTime        @default(now())
  reservations      Reservation[]
  updatedAt         DateTime        @updatedAt
}

model Reservation {
  id              String            @id @default(cuid())
  flightNumber    String?
  startDate       DateTime
  endDate         DateTime
  startPlace      String?
  endPlace        String?
  status          ReservationStatus @default(PENDING)
  isPublished     Boolean           @default(false)
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  userId          String
  carId           String?
  
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  car             Car?     @relation(fields: [carId], references: [id])

  @@map("reservations")
}


enum ReservationStatus {
  PENDING
  CANCELLED
  CONFIRMED
}

enum CarCategory {
  CITADINE
  BERLINE
  FOUR_BY_FOUR
  LUXE
}