"use client";

import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { LucideIcon } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";

interface SidebarItemProps {
  icon: LucideIcon;
  label: string;
  href: string;
}

export const SidebarItem = ({ icon: Icon, label, href }: SidebarItemProps) => {
  const pathname = usePathname();
  const router = useRouter();

  const isActive =
    (pathname === "/dashboard" && href === "/dashboard") ||
    pathname === href ||
    pathname?.startsWith(`${href}/`);

  const onClick = () => {
    router.push(href);
  };

  return (
    <button
      onClick={onClick}
      type="button"
      className={cn(
        "w-full flex items-center gap-x-3 text-gray-600 dark:text-gray-300 text-sm font-medium px-3 py-3 rounded-xl transition-all duration-200 hover:text-yellow-600 hover:bg-yellow-50 dark:hover:text-yellow-400 dark:hover:bg-yellow-900/20 group",
        isActive &&
          "text-yellow-600 dark:text-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/30 dark:to-orange-900/30 border border-yellow-200 dark:border-yellow-800 shadow-sm"
      )}
    >
      <div
        className={cn(
          "p-2 rounded-lg transition-colors",
          isActive
            ? "bg-gradient-to-r from-yellow-400 to-orange-500 text-white shadow-md"
            : "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 group-hover:bg-yellow-100 dark:group-hover:bg-yellow-900/40"
        )}
      >
        <Icon size={18} />
      </div>
      <span className="font-medium">{label}</span>
      {isActive && (
        <div className="ml-auto w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
      )}
    </button>
  );
};
