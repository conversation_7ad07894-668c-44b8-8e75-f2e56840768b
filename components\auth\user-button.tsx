"use client";

import { FaUser } from "react-icons/fa";
import { useCurrentUser } from "@/hooks/use-current-user";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { Button } from "@/components/ui/button";

const UserButton = () => {
  const user = useCurrentUser();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className="h-10 px-4 border-yellow-400 text-yellow-600 hover:bg-yellow-50 hover:text-yellow-700"
        >
          <FaUser className="mr-2 h-4 w-4" />
          {user?.firstName || "Utilisateur"}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <div className="flex items-center justify-start gap-2 p-2">
          <div className="flex flex-col space-y-1 leading-none">
            {user?.firstName && (
              <p className="font-medium text-sm">
                {user.firstName} {user.lastName}
              </p>
            )}
            {user?.email && (
              <p className="w-[200px] truncate text-xs text-muted-foreground">
                {user.email}
              </p>
            )}
          </div>
        </div>
        <DropdownMenuItem>
          <FaUser className="mr-2 h-4 w-4" />
          <span>Profil</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default UserButton;
