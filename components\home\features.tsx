import { Car, Shield, Cloud, CreditCard, Monitor, Users } from "lucide-react";
import { IconBadge } from "../shared/icon-badge";

const features = [
  {
    icon: Car,
    title: "Location de voitures",
    description:
      "Louez des voitures adaptées à vos besoins, que ce soit pour un voyage d'affaires ou un déplacement professionnel.",
  },
  {
    icon: Shield,
    title: "Sécurité",
    description:
      "Protégez vos biens avec nos véhicules entièrement assurés pour vous offrir une tranquillité d'esprit totale.",
  },
  {
    icon: Cloud,
    title: "Réservation en ligne",
    description:
      "Réservez votre véhicule en ligne, à tout moment, de manière rapide et facile. Une expérience de location moderne et simple.",
  },
  {
    icon: CreditCard,
    title: "Tarification transparente",
    description:
      "Nous offrons une tarification claire et sans surprise. Vous payez seulement pour ce que vous utilisez.",
  },
  {
    icon: Monitor,
    title: "Gestion de flotte",
    description:
      "<PERSON><PERSON>rez et surveillez vos locations de véhicules à partir d'une plateforme centralisée, accessible en ligne.",
  },
  {
    icon: Users,
    title: "Service clientèle",
    description:
      "Bénéficiez d'un support client dédié et réactif, disponible à tout moment pour répondre à vos besoins.",
  },
];

const Features: React.FC = () => {
  return (
    <div className="w-full my-32">
      <div className="px-8 py-16">
        <div className="max-w-screen-md mb-12 lg:mb-20 text-center mx-auto">
          <p className="text-sm font-medium text-yellow-600 dark:text-yellow-400 mb-6">
            Nos Avantages
          </p>

          <h2 className="mb-6 font-bold text-gray-800 text-4xl lg:text-5xl dark:text-neutral-200">
            Conçu pour les équipes d'affaires comme la vôtre
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed max-w-2xl mx-auto">
            Chez nous, nous nous concentrons sur des solutions de location de
            voitures flexibles et pratiques pour toutes vos affaires.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="p-6 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700"
            >
              <div className="w-fit p-3 rounded-lg bg-yellow-500 text-white mb-4">
                <feature.icon className="w-6 h-6" />
              </div>

              <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-3">
                {feature.title}
              </h3>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Features;
